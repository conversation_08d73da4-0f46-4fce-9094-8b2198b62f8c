{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:mama_tattletail", "is_spawnable": true, "is_summonable": true, "properties": {"ditsh:playing_music": {"type": "bool", "client_sync": false, "default": false}, "ditsh:storytelling": {"type": "bool", "client_sync": true, "default": false}}, "animations": {"teleport_controller": "controller.animation.ithiefi_ditsh_mama_tattletail.teleport"}, "scripts": {"animate": ["teleport_controller"]}}, "component_groups": {"ditsh:music_playing": {"minecraft:timer": {"looping": true, "time": 39, "time_down_event": {"event": "ditsh:maintain_chase_music"}}}, "ditsh:story_playing": {"minecraft:timer": {"looping": false, "time": 36, "time_down_event": {"event": "ditsh:on_finish_story"}}, "minecraft:is_collidable": {}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": "no"}]}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": false}, "minecraft:ambient_sound_interval": {"event_name": "ambient", "range": 99999, "value": 99999}}, "ditsh:active": {"minecraft:behavior.melee_box_attack": {"priority": 1, "track_target": true, "horizontal_reach": 0.8, "cooldown_time": 1, "can_spread_on_fire": true, "speed_multiplier": 1.2, "on_kill": {"event": "ditsh:on_kill", "target": "self"}}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 0, "target_search_height": -1, "reselect_targets": true, "cooldown": 0, "attack_interval": 0, "must_reach": false, "must_see": false, "reevaluate_description": true, "entity_types": [{"priority": 0, "filters": {"all_of": [{"test": "is_moving", "subject": "other", "value": true}, {"test": "is_family", "subject": "other", "value": "player"}]}, "must_see": false, "max_dist": 256}]}, "minecraft:behavior.hurt_by_target": {"priority": 1, "entity_types": {"filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "reevaluate_description": false, "max_dist": 256}}, "minecraft:behavior.random_look_around": {"priority": 5}, "minecraft:ambient_sound_interval": {"event_name": "ambient", "range": 5, "value": 2}, "minecraft:pushable": {}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["ditsh:story_playing"]}, "queue_command": {"command": "playsound mob.ditsh.mama_tattletail.story @a ~ ~ ~"}, "set_property": {"ditsh:storytelling": true}}, "ditsh:on_finish_story": {"remove": {"component_groups": ["ditsh:story_playing"]}, "add": {"component_groups": ["ditsh:active"]}, "set_property": {"ditsh:storytelling": false}}, "ditsh:on_death": {}, "ditsh:on_kill": {"queue_command": {"command": "playsound mob.ditsh.mama_tattletail.kill @a ~ ~ ~"}}, "ditsh:start_chase_music": {"add": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": true}}, "ditsh:stop_chase_music": {"remove": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": false}}, "ditsh:maintain_chase_music": {}, "ditsh:teleport": {}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 12 + (query.equipment_count * Math.Random(16,24)) : 0"}, "minecraft:type_family": {"family": ["ditsh", "mama_tattletail", "mob", "monster"]}, "minecraft:collision_box": {"width": 0.8, "height": 1.3}, "minecraft:health": {"value": 80, "max": 80}, "minecraft:variable_max_auto_step": {"base_value": 1.0625, "jump_prevented_value": 1.0625}, "minecraft:movement": {"value": 0.2}, "minecraft:movement.basic": {}, "minecraft:navigation.walk": {"can_open_doors": true, "can_open_iron_doors": true, "avoid_damage_blocks": true, "avoid_water": false, "can_path_over_water": true, "can_path_over_lava": true, "can_sink": false, "is_amphibious": true}, "minecraft:knockback_resistance": {"value": 0.7, "max": 1}, "minecraft:behavior.float": {"priority": 0}, "minecraft:attack": {"damage": 10}, "minecraft:follow_range": {"value": 1024, "max": 1024}, "minecraft:jump.static": {}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:on_death": {"event": "ditsh:on_death", "target": "self"}, "minecraft:environment_sensor": {"triggers": [{"event": "ditsh:start_chase_music", "filters": {"all_of": [{"test": "has_target", "subject": "self", "value": true}, {"test": "bool_property", "subject": "self", "domain": "ditsh:playing_music", "value": false}]}}, {"event": "ditsh:stop_chase_music", "filters": {"all_of": [{"test": "has_target", "subject": "self", "value": false}, {"test": "bool_property", "subject": "self", "domain": "ditsh:playing_music", "value": true}]}}]}}}}