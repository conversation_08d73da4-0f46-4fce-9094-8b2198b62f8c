{
  "format_version": "1.21.70",
  "minecraft:block": {
    "description": {
      "identifier": "ditsh:stairs",
      "menu_category": { "category": "construction" },
      "traits": {
        "minecraft:placement_position": { "enabled_states": ["minecraft:vertical_half"] },
        "minecraft:placement_direction": { "enabled_states": ["minecraft:cardinal_direction"] }
      },
      "states": { "ditsh:type": [1, 2, 3, 4, 5] }
    },
    "components": {
      "minecraft:item_visual": {
        "geometry": {
          "identifier": "geometry.ithiefi_ditsh_stairs",
          "bone_visibility": {
            "bot_ne": true,
            "bot_nw": true,
            "bot_se": true,
            "bot_sw": true,
            "top_ne": true,
            "top_nw": true,
            "top_se": false,
            "top_sw": false
          }
        },
        "material_instances": { "*": { "texture": "ditsh_stairs", "render_method": "opaque" } }
      },
      "minecraft:material_instances": {
        "*": { "texture": "ditsh_stairs", "render_method": "opaque", "isotropic": true }
      },
      "tag:minecraft:is_pickaxe_item_destructible": {},
      "tag:ditsh:stair": {},
      "tag:stone": {}
    },
    "permutations": [
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom'",
        "components": {
          "minecraft:collision_box": { "origin": [-8, 0, -8], "size": [16, 8, 16] },
          "minecraft:selection_box": { "origin": [-8, 0, -8], "size": [16, 8, 16] },
          "tag:ditsh:bottom_half": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top'",
        "components": {
          "minecraft:collision_box": { "origin": [-8, 8, -8], "size": [16, 8, 16] },
          "minecraft:selection_box": { "origin": [-8, 8, -8], "size": [16, 8, 16] },
          "tag:ditsh:top_half": {}
        }
      },

      //bot 1
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 1",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": true,
              "top_se": false,
              "top_sw": false
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["down", "north"]
              }
            ]
          },
          "tag:ditsh:north": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 1",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": false,
              "top_nw": false,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["down", "south"]
              }
            ]
          },
          "tag:ditsh:south": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 1",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": false,
              "top_se": true,
              "top_sw": false
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["down", "east"]
              }
            ]
          },
          "tag:ditsh:east": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 1",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": false,
              "top_nw": true,
              "top_se": false,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["down", "west"]
              }
            ]
          },
          "tag:ditsh:west": {}
        }
      },

      //bot 2
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 2",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": true,
              "top_se": false,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["down", "north", "west"]
              }
            ]
          },
          "tag:ditsh:north": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 2",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": false,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["down", "south", "east"]
              }
            ]
          },
          "tag:ditsh:south": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 2",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": false
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["down", "north", "east"]
              }
            ]
          },
          "tag:ditsh:east": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 2",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": false,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["down", "south", "west"]
              }
            ]
          },
          "tag:ditsh:west": {}
        }
      },

      //bot 3
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 3",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": false
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["down", "north", "east"]
              }
            ]
          },
          "tag:ditsh:north": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 3",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": false,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["down", "south", "west"]
              }
            ]
          },
          "tag:ditsh:south": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 3",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": false,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["down", "south", "east"]
              }
            ]
          },
          "tag:ditsh:east": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 3",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": true,
              "top_se": false,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["down", "north", "west"]
              }
            ]
          },
          "tag:ditsh:west": {}
        }
      },

      //bot 4
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 4",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": false,
              "top_nw": true,
              "top_se": false,
              "top_sw": false
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"] }
            ]
          },
          "tag:ditsh:north": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 4",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": false,
              "top_nw": false,
              "top_se": false,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"] }
            ]
          },
          "tag:ditsh:south": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 4",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": false,
              "top_nw": false,
              "top_se": true,
              "top_sw": false
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"] }
            ]
          },
          "tag:ditsh:east": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 4",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": false,
              "top_nw": false,
              "top_se": false,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"] }
            ]
          },
          "tag:ditsh:west": {}
        }
      },

      //bot 5
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 5",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": false,
              "top_se": false,
              "top_sw": false
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"] }
            ]
          },
          "tag:ditsh:north": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 5",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": false,
              "top_nw": false,
              "top_se": true,
              "top_sw": false
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"] }
            ]
          },
          "tag:ditsh:south": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 5",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": false,
              "top_se": false,
              "top_sw": false
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"] }
            ]
          },
          "tag:ditsh:east": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'bottom' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 5",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": false,
              "top_nw": true,
              "top_se": false,
              "top_sw": false
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["down"] }
            ]
          },
          "tag:ditsh:west": {}
        }
      },

      //top 1
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 1",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": false,
              "bot_sw": false,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["up", "north"]
              }
            ]
          },
          "tag:ditsh:north": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 1",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": false,
              "bot_nw": false,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["up", "south"]
              }
            ]
          },
          "tag:ditsh:south": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 1",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": false,
              "bot_se": true,
              "bot_sw": false,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["up", "east"]
              }
            ]
          },
          "tag:ditsh:east": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 1",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": false,
              "bot_nw": true,
              "bot_se": false,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["up", "west"]
              }
            ]
          },
          "tag:ditsh:west": {}
        }
      },

      //top 2
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 2",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": false,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["up", "north", "west"]
              }
            ]
          },
          "tag:ditsh:north": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 2",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": false,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["up", "south", "east"]
              }
            ]
          },
          "tag:ditsh:south": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 2",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": false,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["up", "south", "west"]
              }
            ]
          },
          "tag:ditsh:east": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 2",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": false,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["up", "north", "east"]
              }
            ]
          },
          "tag:ditsh:west": {}
        }
      },

      //top 3
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 3",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": false,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["up", "north", "east"]
              }
            ]
          },
          "tag:ditsh:north": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 3",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": false,
              "bot_nw": true,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["up", "south", "west"]
              }
            ]
          },
          "tag:ditsh:south": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 3",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": false,
              "bot_se": true,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["up", "south", "east"]
              }
            ]
          },
          "tag:ditsh:east": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 3",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": true,
              "bot_se": false,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              {
                "liquid_type": "water",
                "can_contain_liquid": true,
                "stops_liquid_flowing_from_direction": ["up", "north", "west"]
              }
            ]
          },
          "tag:ditsh:west": {}
        }
      },

      //top 4
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 4",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": false,
              "bot_nw": true,
              "bot_se": false,
              "bot_sw": false,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"] }
            ]
          },
          "tag:ditsh:north": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 4",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": false,
              "bot_nw": false,
              "bot_se": false,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"] }
            ]
          },
          "tag:ditsh:south": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 4",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": false,
              "bot_nw": false,
              "bot_se": true,
              "bot_sw": false,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"] }
            ]
          },
          "tag:ditsh:east": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 4",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": false,
              "bot_nw": false,
              "bot_se": false,
              "bot_sw": true,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"] }
            ]
          },
          "tag:ditsh:west": {}
        }
      },

      //top 5
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'north' && q.block_state('ditsh:type') == 5",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": false,
              "bot_se": false,
              "bot_sw": false,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"] }
            ]
          },
          "tag:ditsh:north": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'south' && q.block_state('ditsh:type') == 5",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": false,
              "bot_nw": false,
              "bot_se": true,
              "bot_sw": false,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"] }
            ]
          },
          "tag:ditsh:south": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'east' && q.block_state('ditsh:type') == 5",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": true,
              "bot_nw": false,
              "bot_se": false,
              "bot_sw": false,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"] }
            ]
          },
          "tag:ditsh:east": {}
        }
      },
      {
        "condition": "q.block_state('minecraft:vertical_half') == 'top' && q.block_state('minecraft:cardinal_direction') == 'west' && q.block_state('ditsh:type') == 5",
        "components": {
          "minecraft:geometry": {
            "identifier": "geometry.ithiefi_ditsh_stairs",
            "bone_visibility": {
              "bot_ne": false,
              "bot_nw": true,
              "bot_se": false,
              "bot_sw": false,
              "top_ne": true,
              "top_nw": true,
              "top_se": true,
              "top_sw": true
            }
          },
          "minecraft:liquid_detection": {
            "detection_rules": [
              { "liquid_type": "water", "can_contain_liquid": true, "stops_liquid_flowing_from_direction": ["up"] }
            ]
          },
          "tag:ditsh:west": {}
        }
      }
    ]
  }
}
