{"format_version": "1.19.0", "animation_controllers": {"controller.animation.ithiefi_ditsh_rosemary.general": {"states": {"default": {"animations": ["idle"], "transitions": [{"walking": "q.ground_speed > 0.3"}, {"running": "q.ground_speed > 0.3 && q.has_target"}, {"attack": "v.attack_time > 0"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "walking": {"animations": ["walk"], "transitions": [{"default": "q.ground_speed < 0.3"}, {"running": "q.ground_speed > 0.3 && q.has_target"}, {"attack": "v.attack_time > 0"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "running": {"animations": ["run"], "transitions": [{"default": "q.ground_speed < 0.3"}, {"walking": "q.ground_speed > 0.3 && !q.has_target"}, {"attack": "v.attack_time > 0"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "attack": {"animations": ["attack"], "transitions": [{"default": "q.ground_speed < 0.3 && v.attack_time <= 0"}, {"walking": "q.ground_speed > 0.3 && v.attack_time <= 0"}], "blend_transition": 0.3, "blend_via_shortest_path": true}}}}}