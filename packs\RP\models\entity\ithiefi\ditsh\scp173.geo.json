{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.ithiefi_ditsh_scp173", "texture_width": 128, "texture_height": 128, "visible_bounds_width": 2, "visible_bounds_height": 4, "visible_bounds_offset": [0, 2, 0]}, "bones": [{"name": "waist", "pivot": [0, 0, 0]}, {"name": "body", "parent": "waist", "pivot": [0, 10, 0.5], "rotation": [15, 0, 0], "cubes": [{"origin": [-5, 10, -3], "size": [10, 10, 7], "uv": [27, 34]}]}, {"name": "leg", "parent": "waist", "pivot": [2.8, 11.5, 0], "rotation": [0, 0, -2], "cubes": [{"origin": [0.80313, 0, -1.59999], "size": [4, 11, 4], "uv": [60, 22]}]}, {"name": "leg2", "parent": "waist", "pivot": [-3, 11.5, 0], "rotation": [0, 0, 2], "cubes": [{"origin": [-4.79781, 0, -1.69998], "size": [4, 11, 4], "uv": [58, 58]}]}, {"name": "body2", "parent": "waist", "pivot": [0, 20, 0.5], "cubes": [{"origin": [-4.995, 18.75839, -5.46393], "size": [10, 13, 7], "inflate": 0.01, "uv": [0, 21]}, {"origin": [-3.995, 31.75839, -4.46393], "size": [8, 1, 5], "uv": [34, 28]}, {"origin": [-2.995, 32.75839, -3.46393], "size": [6, 1, 3], "uv": [38, 9]}]}, {"name": "head", "parent": "body2", "pivot": [1, 34.17984, -0.48074], "rotation": [-3, 0, 0], "cubes": [{"origin": [5.005, 35.59714, -5.43582], "size": [1, 12, 8], "uv": [18, 51]}, {"origin": [-4.995, 35.59714, -6.43582], "size": [10, 12, 9], "uv": [0, 0]}, {"origin": [-4.395, 41.59714, -6.53582], "size": [3, 3, 1], "uv": [0, 124]}, {"origin": [1.605, 41.59714, -6.53582], "size": [3, 3, 1], "uv": [0, 124]}, {"origin": [1.605, 38.59714, -6.53582], "size": [2, 2, 1], "uv": [122, 125]}, {"origin": [-0.595, 39.99714, -6.53582], "size": [1.2, 4, 1], "uv": [116, 123]}, {"origin": [-0.495, 39.69714, -6.53582], "size": [1, 1, 1], "uv": [116, 123]}, {"origin": [-0.495, 38.19714, -6.53582], "size": [1, 1, 1], "uv": [116, 123]}, {"origin": [-3.395, 38.59714, -6.53582], "size": [2, 2, 1], "uv": [122, 125]}, {"origin": [-4.995, 34.59714, -5.43582], "size": [10, 1, 8], "uv": [29, 0]}, {"origin": [-5.995, 35.59714, -5.43582], "size": [1, 12, 8], "uv": [0, 41]}, {"origin": [-4.995, 47.59714, -5.43582], "size": [10, 1, 8], "uv": [30, 13]}, {"origin": [-4.995, 35.59714, 2.56418], "size": [10, 12, 1], "uv": [36, 51]}, {"origin": [-3.995, 33.59714, -4.43582], "size": [8, 1, 5], "uv": [27, 22]}]}, {"name": "arm3", "parent": "waist", "pivot": [-5, 29, 1], "rotation": [20, 0, 0], "cubes": [{"origin": [-7.995, 27.56801, -5.54223], "size": [3, 3, 5], "uv": [57, 0]}]}, {"name": "arm", "parent": "waist", "pivot": [6, 29, 1], "rotation": [20, 0, 0], "cubes": [{"origin": [5.005, 27.56801, -5.54223], "size": [3, 3, 5], "uv": [58, 46]}]}, {"name": "arm2", "parent": "waist", "pivot": [6, 29, -3], "cubes": [{"origin": [5, 25.4218, -9.66292], "size": [3, 3, 5], "inflate": 0.001, "uv": [58, 9]}]}, {"name": "arm4", "parent": "waist", "pivot": [-7, 28.25565, -2.77859], "cubes": [{"origin": [-7.99, 25.4218, -9.66292], "size": [3, 3, 5], "inflate": 0.005, "uv": [10, 41]}]}]}]}