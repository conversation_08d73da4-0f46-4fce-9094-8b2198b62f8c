{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:slenderman", "is_spawnable": true, "is_summonable": true}, "component_groups": {"ditsh:active": {"minecraft:looked_at": {"search_radius": 128.0, "look_at_locations": [{"location": "head"}, {"location": "body"}, {"location": "feet", "vertical_offset": 0.5}], "set_target": "never", "find_players_only": true, "looked_at_cooldown": 0.1, "field_of_view": 5, "scale_fov_by_distance": false, "line_of_sight_obstruction_type": "collision_for_camera", "looked_at_event": {"event": "ditsh:on_player_start_looking", "target": "self"}, "filters": {"test": "actor_health", "subject": "other", "operator": ">", "value": 0}}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["ditsh:active"]}}, "ditsh:on_kill": {"queue_command": {"command": "playsound mob.ditsh.slenderman.kill @a ~ ~ ~"}}, "ditsh:on_spot_player": {"queue_command": {"command": "playsound mob.ditsh.slenderman.spot @a ~ ~ ~"}}, "ditsh:on_player_start_looking": {}, "ditsh:teleport_to_player": {"queue_command": {"command": "say I see you!"}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 12 + (query.equipment_count * Math.Random(1,3)) : 0"}, "minecraft:type_family": {"family": ["ditsh", "slenderman", "mob", "monster"]}, "minecraft:collision_box": {"width": 0.4, "height": 2.9}, "minecraft:health": {"value": 50, "max": 50}, "minecraft:variable_max_auto_step": {"base_value": 1.0625, "jump_prevented_value": 1.0625}, "minecraft:movement": {"value": 0.2}, "minecraft:movement.basic": {}, "minecraft:navigation.walk": {"can_open_doors": true, "can_open_iron_doors": true, "avoid_damage_blocks": true, "avoid_water": false, "can_path_over_water": true, "can_path_over_lava": true, "can_sink": false, "is_amphibious": true}, "minecraft:behavior.float": {"priority": 0}, "minecraft:attack": {"damage": 3.5}, "minecraft:behavior.melee_box_attack": {"priority": 1, "track_target": true, "horizontal_reach": 0.8, "cooldown_time": 1, "can_spread_on_fire": true, "speed_multiplier": 1.1, "on_kill": {"event": "ditsh:on_kill", "target": "self"}}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 0, "target_search_height": -1, "reselect_targets": true, "cooldown": 0, "attack_interval": 0, "must_reach": false, "must_see": false, "reevaluate_description": true, "entity_types": [{"priority": 0, "filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "max_dist": 256}]}, "minecraft:behavior.hurt_by_target": {"priority": 1, "entity_types": {"filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "reevaluate_description": false, "max_dist": 256}}, "minecraft:behavior.random_look_around": {"priority": 5}, "minecraft:behavior.random_stroll": {"priority": 4}, "minecraft:follow_range": {"value": 1024, "max": 1024}, "minecraft:jump.static": {}, "minecraft:pushable": {}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:on_target_acquired": {"event": "ditsh:on_spot_player", "target": "self"}, "minecraft:environment_sensor": {"triggers": [{"filters": {"test": "target_distance", "subject": "other", "operator": ">", "value": 32}, "event": "ditsh:teleport_to_player"}]}}}}