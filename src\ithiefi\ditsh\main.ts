/**
 * @fileoverview Main Entry Point for DitSH Add-On
 *
 * This is the main entry point for the DitSH (Doors in the Silent Hill) Minecraft Bedrock add-on.
 * It initializes all core systems and event listeners required for the add-on to function properly.
 *
 * The add-on provides:
 * - Interactive door entities with cardinal direction alignment
 * - Dynamic music system for specific entities (like A<PERSON> Oni)
 * - Entity behavior management and event handling
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 *
 * @remarks
 * This file is the script entry point defined in the behavior pack manifest.json.
 * It should only contain initialization code and imports of core systems.
 *
 * @example
 * ```json
 * // In manifest.json
 * {
 *   "type": "script",
 *   "entry": "scripts/ithiefi/ditsh/main.js",
 *   "language": "javascript"
 * }
 * ```
 */

import { world, system, GameMode } from "@minecraft/server";
import { initEntityListeners } from "./entities/index";
import { handleAllItems, registerSandwichComponent } from "./items/index";

// Werewolf fog management system
const WEREWOLF_FOG_ID: string = "ditsh:werewolf_fog";
const DETECTION_RADIUS: number = 128;

// Run every 1 second (20 ticks) to check all players for werewolf fog and handle all custom items
system.runInterval(() => {
  try {
    // Handle all custom item effects
    handleAllItems();

    // Get all players from all dimensions
    const allPlayers = world
      .getAllPlayers()
      .filter((player) => player.getGameMode() !== GameMode.Creative && player.getGameMode() !== GameMode.Spectator);

    for (const player of allPlayers) {
      try {
        // Check if there are any werewolves within 128 blocks of this player
        const nearbyWerewolves = player.dimension.getEntities({
          type: "ditsh:werewolf",
          location: player.location,
          maxDistance: DETECTION_RADIUS
        });

        const hasNearbyWerewolf = nearbyWerewolves.length > 0;
        const currentlyHasFog = player.getDynamicProperty("ditsh:werewolf_fog") === true;

        if (hasNearbyWerewolf && !currentlyHasFog) {
          // Apply fog
          player.runCommand(`fog @s push ${WEREWOLF_FOG_ID} werewolf`);
          player.setDynamicProperty("ditsh:werewolf_fog", true);
        } else if (!hasNearbyWerewolf && currentlyHasFog) {
          // Remove fog
          player.runCommand(`fog @s remove werewolf`);
          player.setDynamicProperty("ditsh:werewolf_fog", false);
        }
      } catch (error) {
        console.warn(`Failed to process werewolf fog for player: ${error}`);
      }
    }
  } catch (error) {
    console.warn(`Failed to run werewolf fog system: ${error}`);
  }
}, 20); // 20 ticks = 1 second

// Player join/respawn fog cleanup
// Clears werewolf fog when players join or respawn
world.afterEvents.playerSpawn.subscribe((event) => {
  try {
    const player = event.player;
    // Clear werewolf fog on spawn/respawn
    player.runCommand(`fog @s remove werewolf`);
    player.setDynamicProperty("ditsh:werewolf_fog", false);
  } catch (error) {
    console.warn(`Failed to clear werewolf fog on player spawn: ${error}`);
  }
});

// Register custom item components during system startup
system.beforeEvents.startup.subscribe((event) => {
  try {
    registerSandwichComponent(event.itemComponentRegistry);
  } catch (error) {
    console.warn(`Failed to register custom components: ${error}`);
  }
});

/**
 * Initialize all core systems for the DitSH add-on.
 *
 * @description This is the main initialization sequence that sets up:
 * - Entity event listeners for spawn, load, and data-driven triggers
 * - Music system for entity-based background music
 * - Door rotation system for proper cardinal alignment
 * - Werewolf fog management system (directly in main.ts)
 * - Player fog cleanup on join/respawn (directly in main.ts)
 * - Custom item components (sandwich functionality via startup event)
 *
 * @remarks
 * This code runs immediately when the script is loaded by Minecraft.
 * All initialization should be synchronous and handle any potential errors gracefully.
 * Custom components are registered during system startup event for proper timing.
 */
initEntityListeners();
