{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:spider_blocksian", "is_spawnable": true, "is_summonable": true, "properties": {"ditsh:playing_music": {"type": "bool", "client_sync": false, "default": false}}}, "component_groups": {"ditsh:music_playing": {"minecraft:timer": {"looping": true, "time": 5, "time_down_event": {"event": "ditsh:maintain_chase_music"}}}}, "events": {"ditsh:on_death": {}, "ditsh:start_chase_music": {"add": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": true}}, "ditsh:stop_chase_music": {"remove": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": false}}, "ditsh:maintain_chase_music": {}, "ditsh:has_target": {"queue_command": {"command": "playsound mob.ditsh.spider_blocksian.spot @p ~ ~ ~"}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 12 + (query.equipment_count * Math.Random(1,3)) : 0"}, "minecraft:type_family": {"family": ["ditsh", "spider_blocksian", "mob", "monster"]}, "minecraft:collision_box": {"width": 0.7, "height": 2.5}, "minecraft:health": {"value": 50, "max": 50}, "minecraft:variable_max_auto_step": {"base_value": 1.0625, "jump_prevented_value": 1.0625}, "minecraft:movement.basic": {}, "minecraft:navigation.walk": {"can_open_doors": true, "can_open_iron_doors": true, "avoid_damage_blocks": true, "avoid_water": false, "can_path_over_water": true, "can_path_over_lava": true, "can_sink": false, "is_amphibious": true}, "minecraft:movement": {"value": 0.2}, "minecraft:underwater_movement": {"value": 0.35}, "minecraft:behavior.float": {"priority": 0}, "minecraft:attack": {"damage": 10}, "minecraft:behavior.melee_box_attack": {"priority": 1, "track_target": true, "horizontal_reach": 0.8, "cooldown_time": 1, "can_spread_on_fire": true, "speed_multiplier": 2.2}, "minecraft:behavior.nearest_attackable_target": {"priority": 1, "within_radius": 128.0, "reselect_targets": true, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "player"}, "max_dist": 128, "must_see": false, "reevaluate_description": false}]}, "minecraft:behavior.hurt_by_target": {"priority": 1, "entity_types": {"filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "reevaluate_description": false, "max_dist": 128}}, "minecraft:behavior.random_look_around": {"priority": 5}, "minecraft:behavior.random_stroll": {"priority": 4}, "minecraft:follow_range": {"value": 128, "max": 128}, "minecraft:jump.static": {}, "minecraft:pushable": {}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:on_death": {"event": "ditsh:on_death", "target": "self"}, "minecraft:environment_sensor": {"triggers": [{"event": "ditsh:start_chase_music", "filters": {"all_of": [{"test": "has_target", "subject": "self", "value": true}, {"test": "bool_property", "subject": "self", "domain": "ditsh:playing_music", "value": false}]}}, {"event": "ditsh:stop_chase_music", "filters": {"all_of": [{"test": "has_target", "subject": "self", "value": false}, {"test": "bool_property", "subject": "self", "domain": "ditsh:playing_music", "value": true}]}}]}, "minecraft:on_target_acquired": {"event": "ditsh:has_target", "target": "self"}}}}