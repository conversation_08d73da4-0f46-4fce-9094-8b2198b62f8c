import { Entity, Entity<PERSON><PERSON><PERSON><PERSON><PERSON>e, GameMode, Player, Vector3 } from "@minecraft/server";
import { getDistance, getRandomLocation } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";

const TELEPORT_OFFSET_DISTANCE = 5;
const LOOK_DAMAGE_AMOUNT = 1;
const NAUSEA_DURATION = 100; // 5 seconds in ticks

// Raycast parameters for line-of-sight detection
const RAYCAST_LENGTH = 128; // Maximum raycast distance
const RAYCAST_STEP = 2; // Step size between raycast points
const RAYCAST_RADIUS = 5; // Radius around each raycast point to check for Slenderman

/**
 * Handles the effects applied to players when they start looking at <PERSON><PERSON><PERSON>.
 * This function is triggered by the looked_at component and checks all players within 128 blocks
 * using fixed length raycast with 2-block steps and 5-block radius detection per raycast point.
 *
 * @param slenderman - The Slenderman entity
 */
export function slendermanOnPlayerStartLooking(slenderman: Entity): void {
  try {
    const slendermanLocation: Vector3 = slenderman.location;

    // Get all valid players within 128 blocks
    const players: Player[] = slenderman.dimension.getPlayers({
      location: slendermanLocation,
      maxDistance: 128,
      excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });

    for (const player of players) {
      if (isPlayerLookingAtSlenderman(player, slendermanLocation)) {
        // Apply nausea effect
        player.addEffect("nausea", NAUSEA_DURATION, { amplifier: 0, showParticles: false });

        // Apply damage
        player.applyDamage(LOOK_DAMAGE_AMOUNT, { cause: EntityDamageCause.entityAttack, damagingEntity: slenderman });
      }
    }
  } catch (error) {
    console.warn(`Failed to apply Slenderman look effects: ${error}`);
  }
}

/**
 * Checks if a player's view direction is pointing at Slenderman's location using fixed length raycast.
 * Uses raycast with 2-block steps and checks for 5-block radius around each raycast point.
 * The detection area extends +3 blocks upward in Y direction from Slenderman's location.
 *
 * @param player - The player to check
 * @param slendermanLocation - Slenderman's current location
 * @returns True if the player is looking at Slenderman's area
 */
function isPlayerLookingAtSlenderman(player: Player, slendermanLocation: Vector3): boolean {
  try {
    const playerLocation: Vector3 = player.getHeadLocation();
    const viewDirection: Vector3 = player.getViewDirection();

    // Use predefined raycast parameters for consistent detection
    const maxDistance: number = RAYCAST_LENGTH;
    const raycastStep: number = RAYCAST_STEP;
    const detectionRadius: number = RAYCAST_RADIUS;

    // Perform fixed length raycast from player's location in their view direction
    const raycastPoints: Vector3[] = fixedLenRaycast(playerLocation, viewDirection, maxDistance, raycastStep);

    // Check each raycast point to see if it's within detection radius of Slenderman
    for (const rayPoint of raycastPoints) {
      // Calculate distance from raycast point to Slenderman
      const distanceToSlenderman: number = getDistance(rayPoint, slendermanLocation);

      // Check if raycast point is within detection radius
      if (distanceToSlenderman <= detectionRadius) {
        // Additional Y-coordinate check: only positive 3 blocks from Slenderman's location
        if (rayPoint.y >= slendermanLocation.y && rayPoint.y <= slendermanLocation.y + 3.0) {
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    console.warn(`Failed to check if player is looking at Slenderman: ${error}`);
    return false;
  }
}

/**
 * Handles the teleportation behavior for Slenderman when he's too far from players.
 * This function is called by the environment sensor when a player is detected beyond 32 blocks.
 * Teleports Slenderman to a position 5 blocks away from the nearest player.
 *
 * @param slenderman - The Slenderman entity
 */
export function slendermanTeleportHandler(slenderman: Entity): void {
  try {
    const location: Vector3 = slenderman.location;

    // Get all valid players in a large radius
    const players: Player[] = slenderman.dimension.getPlayers({
      location: location,
      maxDistance: 256,
      excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });

    if (players.length === 0) {
      return;
    }

    // Find the nearest player (environment sensor already confirmed one is too far)
    let nearestPlayer: Player = players[0]!;
    let nearestDistance: number = getDistance(location, nearestPlayer.location);

    for (const player of players) {
      const distance: number = getDistance(location, player.location);
      if (distance < nearestDistance) {
        nearestDistance = distance;
        nearestPlayer = player;
      }
    }

    // Try multiple teleportation strategies with retry system
    let finalTeleportLocation: Vector3 | undefined;

    // Strategy 1: Try teleporting 3 blocks in front of the player (based on view direction)
    const playerViewDirection: Vector3 = nearestPlayer.getViewDirection();
    const inFrontOfPlayerLocation: Vector3 = {
      x: nearestPlayer.location.x + playerViewDirection.x * 3,
      y: nearestPlayer.location.y,
      z: nearestPlayer.location.z + playerViewDirection.z * 3
    };

    const inFrontPlayerBlock = slenderman.dimension.getBlock(inFrontOfPlayerLocation);
    if (inFrontPlayerBlock?.isAir) {
      finalTeleportLocation = inFrontOfPlayerLocation;
    }

    // Strategy 2: Try teleporting 5 blocks behind the player (fallback from original strategy)
    if (!finalTeleportLocation) {
      const behindPlayerLocation: Vector3 = {
        x: nearestPlayer.location.x - playerViewDirection.x * TELEPORT_OFFSET_DISTANCE,
        y: nearestPlayer.location.y,
        z: nearestPlayer.location.z - playerViewDirection.z * TELEPORT_OFFSET_DISTANCE
      };

      const behindPlayerBlock = slenderman.dimension.getBlock(behindPlayerLocation);
      if (behindPlayerBlock?.isAir) {
        finalTeleportLocation = behindPlayerLocation;
      }
    }

    // Strategy 3: If behind player failed, try random locations around the player
    if (!finalTeleportLocation) {
      finalTeleportLocation = getRandomLocation(
        nearestPlayer.location,
        slenderman.dimension,
        3, // baseOffset: 3 blocks minimum distance
        5, // additionalOffset: up to 8 blocks total distance (3+5)
        0, // randomYOffset: same Y level as player
        true // checkForAirBlock: ensure safe teleportation
      );
    }

    // Strategy 4: If random locations failed, try closer to player with smaller range
    if (!finalTeleportLocation) {
      finalTeleportLocation = getRandomLocation(
        nearestPlayer.location,
        slenderman.dimension,
        2, // baseOffset: 2 blocks minimum distance
        2, // additionalOffset: up to 4 blocks total distance (2+2)
        0, // randomYOffset: same Y level as player
        true // checkForAirBlock: ensure safe teleportation
      );
    }

    // Strategy 5: Final fallback - teleport directly to player location
    if (!finalTeleportLocation) {
      finalTeleportLocation = { x: nearestPlayer.location.x, y: nearestPlayer.location.y, z: nearestPlayer.location.z };
    }

    // Teleport Slenderman
    slenderman.teleport(finalTeleportLocation);

    // Play teleport sound effect
    slenderman.dimension.playSound("mob.ditsh.slenderman.spot", finalTeleportLocation);
  } catch (error) {
    console.warn(`Failed to teleport Slenderman: ${error}`);
  }
}
