/**
 * @fileoverview Sandwich Item Handler
 *
 * This module handles the sandwich item functionality in the DitSH add-on.
 * The sandwich is a special food item that can be eaten when hunger is full,
 * provides regeneration and swiftness effects, and locks in the player's inventory.
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 *
 * @remarks
 * The sandwich uses a custom item component system to handle consumption events
 * and apply effects directly through TypeScript rather than JSON food components.
 * This allows for more complex behavior like inventory locking.
 *
 * @example
 * ```typescript
 * // Register the sandwich component in main initialization
 * world.beforeEvents.worldInitialize.subscribe((event) => {
 *   event.itemComponentRegistry.registerCustomComponent("ditsh:sandwich", new SandwichComponent());
 * });
 * ```
 */

import {
  Player,
  ItemComponentConsumeEvent,
  EntityEquippableComponent,
  EntityComponentTypes,
  ItemLockMode,
  EquipmentSlot,
  ItemStack,
  system
} from "@minecraft/server";

/**
 * Custom component for the sandwich item.
 * Handles consumption events and applies effects with inventory locking.
 *
 * @description This component:
 * - Listens for sandwich consumption events
 * - Applies regeneration and swiftness effects for 30 seconds each
 * - Locks the sandwich item in the player's inventory slot
 * - Prevents the item from being moved, dropped, or crafted with
 *
 * @remarks
 * - Uses ItemLockMode.slot for maximum item restriction
 * - Effects are applied directly through TypeScript for precise control
 * - Item locking ensures the sandwich remains in the player's inventory permanently
 * - The component is registered with the identifier "ditsh:sandwich"
 */
export class SandwichComponent {
  /**
   * Called when the sandwich item is consumed by a player.
   * This is the main handler for sandwich functionality.
   *
   * @param event - The ItemComponentConsumeEvent containing consumption details
   *
   * @description This function:
   * - Verifies the consumer is a player
   * - Applies regeneration effect for 30 seconds (600 ticks)
   * - Applies swiftness effect for 30 seconds (600 ticks)
   * - Creates a new sandwich item and places it in the player's main hand
   * - Locks the new sandwich item to prevent removal
   * - Ensures the item persists on death
   *
   * @remarks
   * - Effects are applied with amplifier 0 (level 1)
   * - showParticles is set to false to avoid visual clutter
   * - New item creation is necessary since the original item is consumed
   * - Uses equippable component for direct main hand access
   * - Uses system.runTimeout to handle timing properly
   *
   * @example
   * ```typescript
   * // This method is called automatically when a player consumes the sandwich
   * // No manual invocation required
   * ```
   */
  onConsume = (event: ItemComponentConsumeEvent): void => {
    try {
      // Verify the source is a player
      if (!event.source || event.source.typeId !== "minecraft:player") {
        return;
      }

      const player: Player = event.source as Player;

      // Apply regeneration effect for 30 seconds (600 ticks)
      player.addEffect("regeneration", 600, {
        amplifier: 0, // Level 1 (amplifier 0)
        showParticles: false
      });

      // Apply swiftness effect for 30 seconds (600 ticks)
      player.addEffect("speed", 600, {
        amplifier: 0, // Level 1 (amplifier 0)
        showParticles: false
      });

      // Add a new sandwich item to the player's main hand and lock it after a short delay
      // This ensures the consumption is complete before adding the new item
      system.runTimeout(() => {
        this.addAndLockSandwichInMainHand(player);
      }, 1); // 1 tick delay

    } catch (error) {
      console.warn(`Failed to handle sandwich consumption: ${error}`);
    }
  };

  /**
   * Adds a new sandwich item to the player's main hand and locks it to prevent removal.
   * This is a private helper method called after sandwich consumption.
   *
   * @param player - The player who consumed the sandwich
   *
   * @description This function:
   * - Creates a new sandwich ItemStack
   * - Places it in the player's main hand using the equippable component
   * - Applies ItemLockMode.slot to prevent moving/dropping
   * - Sets keepOnDeath to true for persistence
   *
   * @remarks
   * - Called after consumption since the original item is already consumed
   * - Uses equippable component for direct main hand access
   * - Handles errors gracefully with console warnings
   * - Ensures the sandwich remains permanently in the player's possession
   *
   * @example
   * ```typescript
   * // Called automatically after sandwich consumption
   * this.addAndLockSandwichInMainHand(player);
   * ```
   */
  private addAndLockSandwichInMainHand = (player: Player): void => {
    try {
      // Get the player's equippable component
      const equippableComponent: EntityEquippableComponent | undefined = player.getComponent(
        EntityComponentTypes.Equippable
      ) as EntityEquippableComponent;

      if (!equippableComponent) {
        console.warn(`Failed to get equippable component for player ${player.name}`);
        return;
      }

      // Create a new sandwich item stack
      const sandwichItem: ItemStack = new ItemStack("ditsh:sandwich", 1);

      // Set the sandwich in the main hand
      equippableComponent.setEquipment(EquipmentSlot.Mainhand, sandwichItem);

      // Get the main hand slot to lock it
      const mainHandSlot = equippableComponent.getEquipmentSlot(EquipmentSlot.Mainhand);

      // Lock the item in the main hand slot
      mainHandSlot.lockMode = ItemLockMode.slot;
      mainHandSlot.keepOnDeath = true;

    } catch (error) {
      console.warn(`Failed to add and lock sandwich in main hand for player ${player.name}: ${error}`);
    }
  };
}

/**
 * @fileoverview Sandwich Item Implementation Notes
 *
 * **Key Features:**
 * - Custom item component using onConsume event
 * - Direct effect application through TypeScript
 * - Main hand item replacement and locking with ItemLockMode.slot
 * - Persistence on death with keepOnDeath property
 *
 * **Integration Requirements:**
 * 1. Register the component in system startup event
 * 2. Add component reference to sandwich item JSON
 * 3. Remove old event listener system from index.ts
 * 4. Update main.ts to use component registration
 *
 * **Effect Details:**
 * - Regeneration I for 30 seconds (600 ticks)
 * - Speed I for 30 seconds (600 ticks)
 * - No particle effects to avoid visual clutter
 *
 * **Locking Mechanism:**
 * - Creates new sandwich item after consumption (since original is consumed)
 * - Places new item directly in main hand using equippable component
 * - Uses ItemLockMode.slot for maximum restriction
 * - Prevents moving, dropping, or crafting with the item
 * - Item persists through death and respawn
 * - Ensures sandwich remains permanently in player's possession
 */
