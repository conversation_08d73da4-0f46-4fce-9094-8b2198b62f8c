/**
 * @fileoverview DitSH Custom Stairs Block Logic
 *
 * Handles automatic stair connection and placement logic for custom stairs in the DitSH add-on.
 * This system provides vanilla-like stair behavior including corner connections and proper orientation.
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */

import { world, system, BlockPermutation, Block } from "@minecraft/server";
/**
 * Type definition for stair connection types
 */
type StairType = 1 | 2 | 3 | 4 | 5;

/**
 * Event handler for when a player places a block.
 * Automatically updates stair connections when a stair block is placed.
 */
world.afterEvents.playerPlaceBlock.subscribe((eventData) => {
  const block: Block = eventData.block;
  if (block.hasTag("ditsh:stair")) {
    updateNeighborStairs(block);
  }
});

/**
 * Event handler for when a player breaks a block.
 * Updates neighboring stairs when a stair block is destroyed.
 */
world.beforeEvents.playerBreakBlock.subscribe((eventData) => {
  const block: Block = eventData.block;
  system.run(() => {
    updateDestroyedStair(block);
  });
});

/**
 * Updates stairs when a stair block is destroyed.
 * This function handles the cleanup of stair connections when a block is removed.
 *
 * @param block - The block that was destroyed
 */
function updateDestroyedStair(block: Block): void {
  updateStair(block);

  const neighbors: (Block | undefined)[] = [block.north(), block.south(), block.east(), block.west()];

  for (const neighbor of neighbors) {
    if (neighbor && neighbor.hasTag("ditsh:stair")) {
      updateStair(neighbor);
    }
  }
}

/**
 * Updates neighboring stairs when a new stair is placed.
 * This function ensures proper stair connections are maintained.
 *
 * @param block - The newly placed stair block
 */
function updateNeighborStairs(block: Block): void {
  updateStair(block);

  const blockHalf: string | number | boolean | undefined = block.permutation.getState("minecraft:vertical_half");

  const neighbors: (Block | undefined)[] = [block.north(), block.south(), block.east(), block.west()];

  for (const neighbor of neighbors) {
    if (
      neighbor &&
      neighbor.permutation.getState("minecraft:vertical_half") === blockHalf &&
      neighbor.hasTag("ditsh:stair")
    ) {
      updateStair(neighbor);
    }
  }
}

/**
 * Updates the stair connection type based on neighboring stairs.
 * This function analyzes adjacent blocks and sets the appropriate stair type for proper connections.
 *
 * @param block - The stair block to update
 */
function updateStair(block: Block): void {
  const northBlock: Block | undefined = block.north();
  const southBlock: Block | undefined = block.south();
  const eastBlock: Block | undefined = block.east();
  const westBlock: Block | undefined = block.west();
  const blockChange: BlockPermutation = block.permutation;

  const stairHalf: string | number | boolean | undefined = block.permutation.getState("minecraft:vertical_half");
  const stairDirection: string | number | boolean | undefined =
    block.permutation.getState("minecraft:cardinal_direction");

  // Get neighbor information
  const northHalf: string | number | boolean | undefined = northBlock?.permutation.getState("minecraft:vertical_half");
  const southHalf: string | number | boolean | undefined = southBlock?.permutation.getState("minecraft:vertical_half");
  const eastHalf: string | number | boolean | undefined = eastBlock?.permutation.getState("minecraft:vertical_half");
  const westHalf: string | number | boolean | undefined = westBlock?.permutation.getState("minecraft:vertical_half");

  const northDirection: string | number | boolean | undefined =
    northBlock?.permutation.getState("minecraft:cardinal_direction");
  const southDirection: string | number | boolean | undefined =
    southBlock?.permutation.getState("minecraft:cardinal_direction");
  const eastDirection: string | number | boolean | undefined =
    eastBlock?.permutation.getState("minecraft:cardinal_direction");
  const westDirection: string | number | boolean | undefined =
    westBlock?.permutation.getState("minecraft:cardinal_direction");

  // Check if neighbors are stairs
  const northIsStair: boolean = northBlock?.hasTag("ditsh:stair") ?? false;
  const southIsStair: boolean = southBlock?.hasTag("ditsh:stair") ?? false;
  const eastIsStair: boolean = eastBlock?.hasTag("ditsh:stair") ?? false;
  const westIsStair: boolean = westBlock?.hasTag("ditsh:stair") ?? false;

  // Default to straight stair (type 1)
  let stairType: StairType = 1;

  // Only connect stairs on the same vertical half
  if (stairDirection === "north") {
    // North-facing stairs
    if (northIsStair && northHalf === stairHalf && northDirection === "west") {
      stairType = 4; // Inner corner NW
    } else if (northIsStair && northHalf === stairHalf && northDirection === "east") {
      stairType = 5; // Inner corner NE
    } else if (southIsStair && southHalf === stairHalf && southDirection === "west") {
      stairType = 2; // Outer corner SW
    } else if (southIsStair && southHalf === stairHalf && southDirection === "east") {
      stairType = 3; // Outer corner SE
    }
  } else if (stairDirection === "south") {
    // South-facing stairs
    if (northIsStair && northHalf === stairHalf && northDirection === "west") {
      stairType = 3; // Outer corner NW
    } else if (northIsStair && northHalf === stairHalf && northDirection === "east") {
      stairType = 2; // Outer corner NE
    } else if (southIsStair && southHalf === stairHalf && southDirection === "west") {
      stairType = 4; // Inner corner SW
    } else if (southIsStair && southHalf === stairHalf && southDirection === "east") {
      stairType = 5; // Inner corner SE
    }
  } else if (stairDirection === "west") {
    // West-facing stairs
    if (westIsStair && westHalf === stairHalf && westDirection === "north") {
      stairType = 5; // Inner corner WN
    } else if (westIsStair && westHalf === stairHalf && westDirection === "south") {
      stairType = 4; // Inner corner WS
    } else if (eastIsStair && eastHalf === stairHalf && eastDirection === "north") {
      stairType = 3; // Outer corner EN
    } else if (eastIsStair && eastHalf === stairHalf && eastDirection === "south") {
      stairType = 2; // Outer corner ES
    }
  } else if (stairDirection === "east") {
    // East-facing stairs
    if (westIsStair && westHalf === stairHalf && westDirection === "north") {
      stairType = 2; // Outer corner WN
    } else if (westIsStair && westHalf === stairHalf && westDirection === "south") {
      stairType = 3; // Outer corner WS
    } else if (eastIsStair && eastHalf === stairHalf && eastDirection === "north") {
      stairType = 5; // Inner corner EN
    } else if (eastIsStair && eastHalf === stairHalf && eastDirection === "south") {
      stairType = 4; // Inner corner ES
    }
  }

  // Apply the stair type
  block.setPermutation(blockChange.withState("ditsh:type" as any, stairType));
}
