{"format_version": "1.19.0", "animation_controllers": {"controller.animation.ithiefi_ditsh_mx.general": {"states": {"default": {"animations": ["idle"], "transitions": [{"walking": "q.ground_speed > 0.3"}, {"attack": "v.attack_time > 0"}, {"jumping": "q.property('ditsh:jumping') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "walking": {"animations": ["move"], "transitions": [{"default": "q.ground_speed < 0.3"}, {"attack": "v.attack_time > 0"}, {"jumping": "q.property('ditsh:jumping') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "attack": {"animations": ["attack"], "transitions": [{"default": "q.ground_speed < 0.3 && v.attack_time <= 0"}, {"walking": "q.ground_speed > 0.3 && v.attack_time <= 0"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "jumping": {"animations": ["jump"], "transitions": [{"airborne": "q.all_animations_finished"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "airborne": {"animations": ["airborne"], "transitions": [{"default": "q.ground_speed < 0.3 && q.is_on_ground"}, {"walking": "q.ground_speed > 0.3 && q.is_on_ground"}], "blend_transition": 0.3, "blend_via_shortest_path": true}}}, "controller.animation.ithiefi_ditsh_mx.move": {"initial_state": "walking", "states": {"walking": {"animations": ["walk"], "transitions": [{"running": "q.has_target"}, {"dashing": "q.property('ditsh:dashing') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "running": {"animations": ["run"], "transitions": [{"walking": "!q.has_target"}, {"dashing": "q.property('ditsh:dashing') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "dashing": {"animations": ["dash"], "transitions": [{"walking": "!q.has_target && q.property('ditsh:dashing') == false"}, {"running": "q.has_target && q.property('ditsh:dashing') == false"}], "blend_transition": 0.3, "blend_via_shortest_path": true}}}}}