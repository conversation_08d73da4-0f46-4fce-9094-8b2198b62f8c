{"format_version": "1.21.90", "minecraft:block": {"description": {"identifier": "ditsh:wall_lamp", "menu_category": {"category": "construction"}, "traits": {"minecraft:placement_direction": {"enabled_states": ["minecraft:cardinal_direction"]}}}, "components": {"minecraft:collision_box": {"origin": [-2, 7, 3], "size": [4, 8.5, 5]}, "minecraft:selection_box": {"origin": [-2, 7, 3], "size": [4, 8.5, 5]}, "minecraft:light_emission": 8, "minecraft:geometry": "geometry.ithiefi_ditsh_wall_lamp", "minecraft:material_instances": {"*": {"texture": "wall_lamp", "render_method": "opaque"}}, "minecraft:destructible_by_mining": {"seconds_to_destroy": 1.0}, "minecraft:friction": 0.35, "minecraft:map_color": "#FFDD44", "minecraft:item_visual": {"geometry": "geometry.ithiefi_ditsh_wall_lamp", "material_instances": {"*": {"texture": "wall_lamp", "render_method": "opaque", "isotropic": true}}}, "tag:minecraft:is_pickaxe_item_destructible": {}, "tag:ditsh:wall_lamp": {}, "tag:glass": {}}, "permutations": [{"condition": "query.block_state('minecraft:cardinal_direction') == 'north'", "components": {"minecraft:transformation": {"rotation": [0, 180, 0]}}}, {"condition": "query.block_state('minecraft:cardinal_direction') == 'south'", "components": {"minecraft:transformation": {"rotation": [0, 0, 0]}}}, {"condition": "query.block_state('minecraft:cardinal_direction') == 'west'", "components": {"minecraft:transformation": {"rotation": [0, -90, 0]}}}, {"condition": "query.block_state('minecraft:cardinal_direction') == 'east'", "components": {"minecraft:transformation": {"rotation": [0, 90, 0]}}}]}}