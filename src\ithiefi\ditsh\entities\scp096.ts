import { Entity, system } from "@minecraft/server";

/**
 * @fileoverview SCP-096 Entity Handler for DitSH Add-On
 *
 * This module handles SCP-096 specific behaviors including the async timer system
 * for transitioning from kill state to sitting state. It replaces the unreliable
 * minecraft:timer component with a script-based solution.
 *
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * Handles the SCP-096 kill event by starting an async timer for sitting transition.
 *
 * @description This function is called when SCP-096 kills a player or becomes calm.
 * It starts a 2-second async timer that will trigger the sitting event. The timer uses
 * proper error handling to gracefully handle cases where the entity dies or gets
 * unloaded before completion.
 *
 * @param entity - The SCP-096 entity that killed a player or became calm
 *
 * @remarks
 * **Timer Behavior:**
 * - Uses `system.waitTicks(40)` for a 2-second delay (40 ticks = 2 seconds)
 * - Implements try-catch blocks to handle entity unloading/death
 * - Each entity instance runs its own timer context, no tracking needed
 *
 * **Error Handling:**
 * - Silently handles entity unloading/death scenarios
 * - Validates entity existence before triggering events
 * - No cleanup needed since each entity has its own function context
 *
 * @example
 * ```typescript
 * // Called from data-driven entity event listener
 * world.afterEvents.dataDrivenEntityTrigger.subscribe((data) => {
 *   if (data.eventId === "ditsh:on_kill" && data.entity.typeId === "ditsh:scp096") {
 *     scp096OnKillHandler(data.entity);
 *   }
 * });
 * ```
 */
export async function scp096OnKillHandler(entity: Entity): Promise<void> {
  try {
    // Validate entity exists and is SCP-096
    if (!entity || !entity.isValid || entity.typeId !== "ditsh:scp096") {
      return;
    }

    // Wait for 2 seconds (40 ticks)
    await system.waitTicks(40);

    // Validate entity still exists after delay
    if (!entity || !entity.isValid) {
      // Entity was unloaded/died during timer, exit silently
      return;
    }

    // Trigger the start_sitting event
    entity.triggerEvent("ditsh:start_sitting");
  } catch (error) {
    // Handle any errors silently (entity unloading, world issues, etc.)
    // Optionally log for debugging (remove in production)
    // console.warn(`SCP-096 sitting timer error:`, error);
  }
}
