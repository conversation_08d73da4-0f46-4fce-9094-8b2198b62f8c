{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ithiefi_ditsh_mx.attack_type": {"initial_state": "default", "states": {"default": {"transitions": [{"select_attack": "q.has_target"}]}, "select_attack": {"on_entry": ["/event entity @s ditsh:select_attack"], "transitions": [{"default": "true"}]}}}, "controller.animation.ithiefi_ditsh_mx.ground_check": {"initial_state": "default", "states": {"default": {"transitions": [{"landed": "q.is_on_ground"}]}, "landed": {"on_entry": ["/event entity @s ditsh:on_ground_landing"], "transitions": [{"default": "true"}]}}}}}